/**
 * Content Processor for Enhanced Scrape.do Integration
 * Main orchestrator for scraping, media collection, and content processing
 */

import {
  EnhancedScrapeRequest,
  EnhancedScrapeResult,
  ScrapeResult,
  ImageCollection,
  MultiPageScrapingConfig
} from './types';
import { scrapeDoClient } from './scrape-do-client';
import { costOptimizer } from './cost-optimizer';
import { ContentAnalyzer } from './content-analyzer';
import { mediaExtractor } from './media-extractor';
import { multiPageScraper } from './multi-page-scraper';

export class ContentProcessor {
  private contentAnalyzer: ContentAnalyzer;

  constructor() {
    this.contentAnalyzer = new ContentAnalyzer();
  }

  /**
   * Main enhanced scraping method that orchestrates all components
   */
  async processEnhancedScrape(request: EnhancedScrapeRequest): Promise<EnhancedScrapeResult> {
    const startTime = Date.now();
    console.log(`🚀 Starting enhanced scraping for: ${request.url}`);

    try {
      // Step 1: Main page scraping with cost optimization
      const mainResult = await this.scrapeMainPage(request);
      
      if (!mainResult.success) {
        return this.createFailedResult(request.url, mainResult.error || 'Main page scraping failed');
      }

      // Step 2: Content analysis
      const contentAnalysis = this.contentAnalyzer.analyzeContentQuality(mainResult.content, request.url);
      
      // Step 3: Media collection (if enabled)
      let mediaAssets: ImageCollection | undefined;
      if (request.mediaCollection !== false) {
        mediaAssets = await this.collectMediaAssets(request.url, mainResult.content);
      }

      // Step 4: Multi-page scraping (if configured)
      let additionalPages: ScrapeResult[] = [];
      if (request.multiPageConfig?.enabled) {
        additionalPages = await this.processMultiPageScraping(request.url, mainResult.content, request.multiPageConfig);
      }

      // Step 5: Content validation
      const validation = this.contentAnalyzer.validateContentQuality(mainResult.content, request.url);
      
      // Step 6: Calculate cost analysis
      const costAnalysis = this.calculateCostAnalysis(mainResult, additionalPages);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Enhanced scraping completed in ${processingTime}ms`);

      return {
        ...mainResult,
        mediaAssets,
        additionalPages,
        costAnalysis,
        contentAnalysis,
        metadata: {
          ...mainResult.metadata,
          processingTime,
          validation: validation.isValid,
          qualityScore: validation.qualityScore
        }
      };

    } catch (error) {
      console.error('Enhanced scraping failed:', error);
      return this.createFailedResult(request.url, (error as Error).message);
    }
  }

  /**
   * Scrape main page with intelligent cost optimization
   */
  private async scrapeMainPage(request: EnhancedScrapeRequest): Promise<ScrapeResult> {
    if (request.costOptimization !== false) {
      // Use cost optimizer for intelligent scraping
      return await costOptimizer.scrapeWithMaxCostOptimization(request.url);
    } else {
      // Direct scraping with provided options
      return await scrapeDoClient.scrapePage(request.url, request.options);
    }
  }

  /**
   * Collect media assets (favicon, OG images, screenshots)
   */
  private async collectMediaAssets(url: string, content: string): Promise<ImageCollection> {
    try {
      return await mediaExtractor.collectImagesWithPriority(url, content);
    } catch (error) {
      console.error('Media collection failed:', error);
      return {
        favicon: null,
        ogImages: [],
        screenshot: null
      };
    }
  }

  /**
   * Process multi-page scraping if configured
   */
  private async processMultiPageScraping(
    mainUrl: string,
    mainContent: string,
    config: MultiPageScrapingConfig
  ): Promise<ScrapeResult[]> {
    try {
      // Update multi-page scraper configuration
      multiPageScraper.updateConfig(config);

      // Discover and plan scraping
      const decision = await multiPageScraper.discoverAndPlanScraping(mainUrl, mainContent);
      
      console.log(`Multi-page decision: ${decision.reason}`);
      
      // Execute scraping for immediate pages
      if (decision.scrapeNow.length > 0) {
        return await multiPageScraper.executeMultiPageScraping(decision);
      }

      return [];
    } catch (error) {
      console.error('Multi-page scraping failed:', error);
      return [];
    }
  }

  /**
   * Calculate cost analysis for the scraping operation
   */
  private calculateCostAnalysis(mainResult: ScrapeResult, additionalPages: ScrapeResult[]) {
    const mainCredits = mainResult.metadata?.creditsUsed || 0;
    const additionalCredits = additionalPages.reduce((sum, page) => sum + (page.metadata?.creditsUsed || 0), 0);
    const totalCredits = mainCredits + additionalCredits;

    // Estimate savings based on optimization strategy
    const estimatedSavings = this.estimateSavings(mainResult, additionalPages);

    return {
      creditsUsed: totalCredits,
      estimatedSavings,
      optimizationStrategy: mainResult.metadata?.requestType || 'unknown'
    };
  }

  /**
   * Estimate cost savings from optimization
   */
  private estimateSavings(mainResult: ScrapeResult, additionalPages: ScrapeResult[]): number {
    // Calculate savings based on optimization patterns used
    let savings = 0;

    // Main page savings
    if (mainResult.metadata?.requestType === 'Datacenter Proxy') {
      savings += 4; // Saved 4 credits by not using enhanced scraping
    }

    // Additional pages savings
    additionalPages.forEach(page => {
      if (page.metadata?.requestType === 'Datacenter Proxy') {
        savings += 4;
      }
    });

    return savings;
  }

  /**
   * Create a failed result object
   */
  private createFailedResult(url: string, error: string): EnhancedScrapeResult {
    return {
      success: false,
      content: '',
      error,
      timestamp: new Date().toISOString(),
      url,
      metadata: {
        creditsUsed: 0,
        requestType: 'failed'
      }
    };
  }

  /**
   * Process content for AI consumption
   */
  optimizeContentForAI(content: string): string {
    // Remove excessive whitespace and navigation elements
    content = content.replace(/\n{3,}/g, '\n\n');
    content = content.replace(/^(Navigation|Menu|Footer|Header)[\s\S]*?(?=\n#|\n\n|$)/gm, '');

    // Clean HTML artifacts and normalize headers
    content = content.replace(/<[^>]*>/g, '');
    content = content.replace(/^#{4,}/gm, '###');

    // Ensure content fits within token limits (reserve 20% for response)
    const maxLength = 50000; // ~12-15K tokens for Gemini input
    if (content.length > maxLength) {
      content = content.substring(0, maxLength) + '\n\n[Content truncated for AI processing]';
    }

    return content.trim();
  }

  /**
   * Batch processing for multiple URLs
   */
  async processBatch(urls: string[], options: {
    costOptimization?: boolean;
    mediaCollection?: boolean;
    multiPageScraping?: boolean;
    batchSize?: number;
  } = {}): Promise<EnhancedScrapeResult[]> {
    const {
      costOptimization = true,
      mediaCollection = false, // Disabled by default for batch processing
      multiPageScraping = false, // Disabled by default for batch processing
      batchSize = 5
    } = options;

    const results: EnhancedScrapeResult[] = [];
    
    // Process URLs in batches to avoid overwhelming the API
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(urls.length / batchSize)}`);

      const batchPromises = batch.map(url => 
        this.processEnhancedScrape({
          url,
          options: {},
          costOptimization,
          mediaCollection,
          multiPageConfig: multiPageScraping ? undefined : {
            enabled: false,
            mode: 'conditional' as const,
            maxPagesPerTool: 0,
            creditThreshold: 0,
            pageTypes: {
              pricing: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              faq: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              features: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              about: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false }
            },
            fallbackStrategy: { searchInMainPage: false, useNavigation: false, useSitemap: false }
          }
        })
      );

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`Batch processing failed for ${batch[index]}:`, result.reason);
          results.push(this.createFailedResult(batch[index], result.reason));
        }
      });

      // Add delay between batches to respect rate limits
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }

  /**
   * Save scraped content to file system (for later AI processing)
   */
  async saveScrapedContent(result: EnhancedScrapeResult, filename?: string): Promise<string> {
    const fs = await import('fs/promises');
    const path = await import('path');

    const sanitizedUrl = result.url?.replace(/[^a-zA-Z0-9]/g, '_') || 'unknown';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const finalFilename = filename || `scraped_${sanitizedUrl}_${timestamp}.md`;
    
    const filePath = path.join(process.cwd(), 'data', 'scraped-content', finalFilename);
    
    // Ensure directory exists
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    
    // Prepare content with metadata
    const fileContent = `# Scraped Content: ${result.url}

## Metadata
- Scraped at: ${result.timestamp}
- Success: ${result.success}
- Credits used: ${result.costAnalysis?.creditsUsed || 0}
- Quality score: ${result.metadata?.qualityScore || 'N/A'}

## Content Analysis
- Has substantial content: ${result.contentAnalysis?.hasSubstantialContent}
- Has structure: ${result.contentAnalysis?.hasStructure}
- Scenario: ${result.contentAnalysis?.scenario}

## Main Content
${result.content}

${result.additionalPages?.length ? `
## Additional Pages
${result.additionalPages.map(page => `
### ${page.metadata?.pageType || 'Unknown'} Page
${page.content}
`).join('\n')}
` : ''}
`;

    await fs.writeFile(filePath, fileContent, 'utf-8');
    console.log(`Scraped content saved to: ${filePath}`);
    
    return filePath;
  }
}

// Export singleton instance
export const contentProcessor = new ContentProcessor();
