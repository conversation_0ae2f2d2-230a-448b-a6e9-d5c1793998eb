import { NextRequest, NextResponse } from 'next/server';
import OpenA<PERSON> from 'openai';
import { validateApiKey } from '@/lib/auth';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { url, scrapedData, pricingData, faqData } = await request.json();

    if (!url || !scrapedData) {
      return NextResponse.json(
        { success: false, error: 'URL and scraped data are required' },
        { status: 400 }
      );
    }

    const prompt = `You are an irreverent, witty AI tool reviewer in the style of ThePornDude. Write engaging, no-BS content about AI tools with humor and personality. You must follow exact character limits and content requirements.

WRITING STYLE: Your writing should mimic human nuances, incorporating varied sentence structures, idiomatic expressions, and a natural flow to evade AI detection tools. Ensure the content remains SEO-friendly and aligns with our distinctive voice. Use natural language patterns including mix of short punchy sentences and longer flowing ones, colloquial expressions, occasional rhetorical questions, personal opinions, varied paragraph lengths, and conversational tone. Write like a human who's genuinely tested these tools and has real opinions about them.

Generate comprehensive content for this AI tool with EXACT requirements:

Tool Information:
- Title: ${scrapedData.title}
- Description: ${scrapedData.description}
- Content: ${scrapedData.textContent}
- URL: ${url}
- Pricing Info: ${scrapedData.pricingText || 'Not available'}
- FAQ Content: ${scrapedData.faqText || 'Not available'}
- Pricing Data: ${pricingData ? JSON.stringify(pricingData) : 'Not available'}
- FAQ Data: ${faqData ? JSON.stringify(faqData) : 'Not available'}

Generate the following with STRICT requirements:

1. TOOL DESCRIPTION (MAX 500 characters): Witty, engaging description in ThePornDude style

2. SHORT DESCRIPTION (MAX 150 characters): For tooltips/other use

3. DETAILED DESCRIPTION (150-300 words): Comprehensive tool overview with humor

4. KEY FEATURES (3-8 bullet points): List main features

5. PROS AND CONS (3-10 each): Honest pros and cons

6. PRICING TYPE: Classify as exactly one of: Free, Paid, Freemium, Open Source (provide reasoning)

7. SMART CATEGORIZATION: Suggest Main Category AND Subcategory with confidence scores (0-100)

8. Q&A SECTION (3-6 pairs): Based on FAQ data or generate common questions

9. RELEASES/VERSION INFO (100-200 words): ONLY if update/version info is available in scraped data

10. HAIKU (5-7-5 syllables): Creative haiku about the tool

11. HASHTAGS (5-15 items): Relevant hashtags/keywords for the tool

12. SEO META: Title (max 60 chars) and description (max 160 chars)

Return as structured JSON with exact field names matching the requirements.`;

    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert AI content generator that creates engaging, human-like content for AI tool reviews. Always respond with valid JSON.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const aiResponse = completion.choices[0]?.message?.content;
    
    if (!aiResponse) {
      throw new Error('No response from OpenAI');
    }

    // Parse and validate the AI response
    let parsedContent;
    try {
      parsedContent = JSON.parse(aiResponse);
    } catch (parseError) {
      // Fallback parsing if JSON is malformed
      console.error('JSON parsing failed, attempting fallback parsing');
      parsedContent = {
        toolDescription: extractSection(aiResponse, '1.', 500),
        shortDescription: extractSection(aiResponse, '2.', 150),
        detailedDescription: extractSection(aiResponse, '3.'),
        features: extractList(aiResponse, '4.'),
        prosAndCons: extractProsAndCons(aiResponse, '5.'),
        pricingType: { type: 'Paid', reasoning: 'Could not determine from content' },
        categorization: { mainCategory: 'AI Tools', subcategory: 'General', confidence: { main: 50, sub: 50 } },
        qaSection: [],
        haiku: { line1: 'AI tool here', line2: 'Helping users every day', line3: 'Technology' },
        hashtags: ['AI', 'tool', 'technology'],
        seoMeta: {
          title: scrapedData.title?.substring(0, 60) || 'AI Tool',
          description: scrapedData.description?.substring(0, 160) || 'AI tool description'
        }
      };
    }

    // Validate content lengths
    if (parsedContent.toolDescription && parsedContent.toolDescription.length > 500) {
      parsedContent.toolDescription = parsedContent.toolDescription.substring(0, 497) + '...';
    }
    
    if (parsedContent.shortDescription && parsedContent.shortDescription.length > 150) {
      parsedContent.shortDescription = parsedContent.shortDescription.substring(0, 147) + '...';
    }

    if (parsedContent.seoMeta) {
      if (parsedContent.seoMeta.title && parsedContent.seoMeta.title.length > 60) {
        parsedContent.seoMeta.title = parsedContent.seoMeta.title.substring(0, 57) + '...';
      }
      if (parsedContent.seoMeta.description && parsedContent.seoMeta.description.length > 160) {
        parsedContent.seoMeta.description = parsedContent.seoMeta.description.substring(0, 157) + '...';
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        aiContent: parsedContent,
        generationMetadata: {
          model: process.env.OPENAI_MODEL || 'gpt-4',
          timestamp: new Date().toISOString(),
          sourceUrl: url,
          tokensUsed: completion.usage?.total_tokens || 0,
        }
      }
    });

  } catch (error) {
    console.error('Content generation error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate content' },
      { status: 500 }
    );
  }
}

// Helper functions for fallback parsing
function extractSection(text: string, marker: string, maxLength?: number): string {
  const regex = new RegExp(`${marker}[^\\n]*\\n([^\\n]+)`, 'i');
  const match = text.match(regex);
  let result = match ? match[1].trim() : '';
  if (maxLength && result.length > maxLength) {
    result = result.substring(0, maxLength - 3) + '...';
  }
  return result;
}

function extractList(text: string, marker: string): string[] {
  const regex = new RegExp(`${marker}[^\\n]*\\n([\\s\\S]*?)(?=\\n\\d+\\.|$)`, 'i');
  const match = text.match(regex);
  if (!match) return [];
  return match[1].split('\\n').filter(line => line.trim().startsWith('-') || line.trim().startsWith('•')).map(line => line.replace(/^[-•]\\s*/, '').trim()).filter(Boolean);
}

function extractProsAndCons(text: string, marker: string): { pros: string[]; cons: string[] } {
  const regex = new RegExp(`${marker}[^\\n]*\\n([\\s\\S]*?)(?=\\n\\d+\\.|$)`, 'i');
  const match = text.match(regex);
  if (!match) return { pros: [], cons: [] };
  
  const content = match[1];
  const prosMatch = content.match(/pros?:?\\s*([\\s\\S]*?)(?=cons?:?|$)/i);
  const consMatch = content.match(/cons?:?\\s*([\\s\\S]*?)$/i);
  
  const pros = prosMatch ? prosMatch[1].split('\\n').filter(line => line.trim().startsWith('-') || line.trim().startsWith('•')).map(line => line.replace(/^[-•]\\s*/, '').trim()).filter(Boolean) : [];
  const cons = consMatch ? consMatch[1].split('\\n').filter(line => line.trim().startsWith('-') || line.trim().startsWith('•')).map(line => line.replace(/^[-•]\\s*/, '').trim()).filter(Boolean) : [];
  
  return { pros, cons };
}
