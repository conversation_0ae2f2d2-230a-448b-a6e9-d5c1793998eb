/**
 * Test script for Scrape.do API Integration
 * Run this to verify the integration is working correctly
 */

import { contentProcessor } from './content-processor';
import { scrapeDoClient } from './scrape-do-client';
import { costOptimizer } from './cost-optimizer';

async function testScrapeDoIntegration() {
  console.log('🧪 Testing Scrape.do API Integration...\n');

  try {
    // Test 1: Basic API connectivity
    console.log('1. Testing API connectivity...');
    const stats = await scrapeDoClient.getUsageStatistics();
    console.log(`✅ API connected. Remaining requests: ${stats.remainingMonthlyRequests}`);

    // Test 2: Cost optimization patterns
    console.log('\n2. Testing cost optimization patterns...');
    const optimizationTestUrls = [
      'https://github.com/microsoft/vscode', // Should use never-enhance (1 credit)
      'https://claude.ai', // Should use always-enhance (5 credits)
      'https://example.com' // Should use intelligent detection
    ];

    const categorized = costOptimizer.categorizeUrlsByPattern(optimizationTestUrls);
    console.log('Never enhance:', categorized.neverEnhance);
    console.log('Always enhance:', categorized.alwaysEnhance);
    console.log('Unknown:', categorized.unknown);

    // Test 3: Basic scraping with multiple URLs
    console.log('\n3. Testing basic scraping...');
    const scrapingTestUrls = [
      'https://httpbin.co/anything', // JSON API response (should be recognized as substantial)
      'https://httpbin.co/html',     // HTML content test
      'https://example.com',         // Simple static page
      'https://httpbin.co/json'      // Pure JSON response
    ];

    for (const testUrl of scrapingTestUrls) {
      console.log(`\n   Testing: ${testUrl}`);
      const basicResult = await scrapeDoClient.scrapePage(testUrl, {
        outputFormat: 'markdown',
        timeout: 15000
      });

      if (basicResult.success) {
        console.log(`   ✅ Success (${basicResult.metadata?.creditsUsed} credits, ${basicResult.content.length} chars)`);
      } else {
        console.log(`   ❌ Failed: ${basicResult.error}`);
      }
    }

    // Test 4: Enhanced scraping workflow
    console.log('\n4. Testing enhanced scraping workflow...');
    const enhancedTestUrl = scrapingTestUrls[0]; // Use first URL for enhanced test
    const enhancedResult = await contentProcessor.processEnhancedScrape({
      url: enhancedTestUrl,
      options: {
        outputFormat: 'markdown',
        timeout: 15000
      },
      costOptimization: true,
      mediaCollection: false // Disable for test
    });

    if (enhancedResult.success) {
      console.log(`✅ Enhanced scraping successful`);
      console.log(`Credits used: ${enhancedResult.costAnalysis?.creditsUsed}`);
      console.log(`Optimization strategy: ${enhancedResult.costAnalysis?.optimizationStrategy}`);
      console.log(`Content analysis: ${enhancedResult.contentAnalysis?.scenario}`);
    } else {
      console.log(`❌ Enhanced scraping failed: ${enhancedResult.error}`);
    }

    console.log('\n🎉 Scrape.do integration test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Export for use in other files
export { testScrapeDoIntegration };

// Run test if this file is executed directly
if (require.main === module) {
  testScrapeDoIntegration();
}
