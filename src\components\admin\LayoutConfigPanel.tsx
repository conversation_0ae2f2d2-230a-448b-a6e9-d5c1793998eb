'use client';

import React, { useState } from 'react';
import { LayoutConfig, defaultLayoutConfig, seamlessLayoutConfig, spacedLayoutConfig, compactLayoutConfig, tallLayoutConfig } from '@/config/layout';

/**
 * Admin Layout Configuration Panel
 * 
 * This component demonstrates how the layout can be dynamically configured.
 * In a real admin panel, this would save configurations to a database or config file.
 */

interface LayoutConfigPanelProps {
  onConfigChange?: (config: LayoutConfig) => void;
}

export function LayoutConfigPanel({ onConfigChange }: LayoutConfigPanelProps) {
  const [activeConfig, setActiveConfig] = useState<LayoutConfig>(defaultLayoutConfig);
  const [selectedPreset, setSelectedPreset] = useState<string>('spaced');

  const presets = {
    spaced: { name: 'Spaced Cards (Current)', config: defaultLayoutConfig },
    seamless: { name: 'Seamless Grid', config: seamlessLayoutConfig },
    compact: { name: 'Compact Layout', config: compactLayoutConfig },
    tall: { name: 'Tall Layout', config: tallLayoutConfig },
  };

  const handlePresetChange = (presetKey: string) => {
    const preset = presets[presetKey as keyof typeof presets];
    if (preset) {
      setSelectedPreset(presetKey);
      setActiveConfig(preset.config);
      onConfigChange?.(preset.config);
    }
  };

  const handleCustomChange = (path: string, value: any) => {
    const newConfig = { ...activeConfig };
    const keys = path.split('.');
    let current: any = newConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setActiveConfig(newConfig);
    setSelectedPreset('custom');
    onConfigChange?.(newConfig);
  };

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6 max-w-2xl">
      <h2 className="text-xl font-bold text-white mb-6">Layout Configuration Panel</h2>
      
      {/* Preset Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-3">
          Layout Presets
        </label>
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(presets).map(([key, preset]) => (
            <button
              key={key}
              onClick={() => handlePresetChange(key)}
              className={`
                px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                ${selectedPreset === key 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-zinc-700 text-gray-300 hover:bg-zinc-600'
                }
              `}
            >
              {preset.name}
            </button>
          ))}
        </div>
      </div>

      {/* Grid Configuration */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Grid Settings</h3>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Gap Between Cards
            </label>
            <select
              value={activeConfig.grid.gap}
              onChange={(e) => handleCustomChange('grid.gap', parseInt(e.target.value))}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            >
              <option value={0}>No Gap (Seamless)</option>
              <option value={1}>Small Gap</option>
              <option value={2}>Medium Gap</option>
              <option value={4}>Large Gap</option>
              <option value={6}>Extra Large Gap</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Desktop Columns
            </label>
            <select
              value={activeConfig.grid.columns.wide}
              onChange={(e) => handleCustomChange('grid.columns.wide', parseInt(e.target.value))}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            >
              <option value={2}>2 Columns</option>
              <option value={3}>3 Columns</option>
              <option value={4}>4 Columns</option>
              <option value={5}>5 Columns</option>
              <option value={6}>6 Columns</option>
            </select>
          </div>
        </div>
      </div>

      {/* Card Configuration */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Card Settings</h3>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Height Mode
            </label>
            <select
              value={activeConfig.card.height.mode}
              onChange={(e) => handleCustomChange('card.height.mode', e.target.value)}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            >
              <option value="auto">Auto Height</option>
              <option value="min-height">Minimum Height</option>
              <option value="fixed">Fixed Height</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Minimum Height (px)
            </label>
            <input
              type="number"
              value={activeConfig.card.height.minHeight}
              onChange={(e) => handleCustomChange('card.height.minHeight', parseInt(e.target.value))}
              min={200}
              max={800}
              step={50}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Border Radius
            </label>
            <select
              value={activeConfig.card.spacing.borderRadius}
              onChange={(e) => handleCustomChange('card.spacing.borderRadius', e.target.value)}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            >
              <option value="none">None (Seamless)</option>
              <option value="sm">Small</option>
              <option value="md">Medium</option>
              <option value="lg">Large</option>
              <option value="xl">Extra Large</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Shadow
            </label>
            <select
              value={activeConfig.card.effects.shadow}
              onChange={(e) => handleCustomChange('card.effects.shadow', e.target.value)}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            >
              <option value="none">None</option>
              <option value="sm">Small</option>
              <option value="md">Medium</option>
              <option value="lg">Large</option>
              <option value="xl">Extra Large</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Hover Shadow
            </label>
            <select
              value={activeConfig.card.effects.hoverShadow}
              onChange={(e) => handleCustomChange('card.effects.hoverShadow', e.target.value)}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            >
              <option value="none">None</option>
              <option value="sm">Small</option>
              <option value="md">Medium</option>
              <option value="lg">Large</option>
              <option value="xl">Extra Large</option>
            </select>
          </div>
        </div>
      </div>

      {/* Scrollable Content Configuration */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Scrollable Content</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Max Height (rem)
            </label>
            <input
              type="number"
              value={activeConfig.scrollableContent.maxHeight}
              onChange={(e) => handleCustomChange('scrollableContent.maxHeight', parseInt(e.target.value))}
              min={8}
              max={32}
              step={2}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Scrollbar Width (px)
            </label>
            <input
              type="number"
              value={activeConfig.scrollableContent.scrollbar.width}
              onChange={(e) => handleCustomChange('scrollableContent.scrollbar.width', parseInt(e.target.value))}
              min={4}
              max={16}
              step={2}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-white"
            />
          </div>
        </div>
      </div>

      {/* Current Configuration Display */}
      <div className="bg-zinc-900 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-300 mb-2">Current Configuration</h4>
        <pre className="text-xs text-gray-400 overflow-x-auto">
          {JSON.stringify(activeConfig, null, 2)}
        </pre>
      </div>
    </div>
  );
}
