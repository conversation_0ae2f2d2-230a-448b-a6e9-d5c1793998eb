'use client';

import React from 'react';
import { ArrowRight } from 'lucide-react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'see-all';
  colorClass?: string;
  textColorClass?: string;
  className?: string;
  showArrow?: boolean;
  disabled?: boolean;
}

export function Button({
  children,
  onClick,
  variant = 'primary',
  colorClass,
  textColorClass = 'text-white',
  className = '',
  showArrow = false,
  disabled = false,
}: ButtonProps) {
  const baseClasses = 'px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2';
  
  let variantClasses = '';
  
  switch (variant) {
    case 'primary':
      variantClasses = colorClass || 'bg-orange-500 hover:bg-orange-400';
      break;
    case 'secondary':
      variantClasses = 'bg-zinc-700 hover:bg-zinc-600 text-gray-300';
      break;
    case 'see-all':
      variantClasses = colorClass || 'bg-sky-500 hover:bg-sky-400';
      break;
  }

  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses} ${textColorClass} ${disabledClasses} ${className}`}
    >
      {children}
      {showArrow && <ArrowRight size={16} />}
    </button>
  );
}
