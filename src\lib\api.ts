import { AITool, <PERSON>Category, Tool<PERSON>ilt<PERSON>, PaginationInfo } from './types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// API client for frontend
export class ApiClient {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}/api${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'API request failed');
    }

    return data.data;
  }

  // Tools API
  async getTools(filters?: ToolFilters): Promise<{
    data: AITool[];
    pagination: PaginationInfo;
  }> {
    const params = new URLSearchParams();
    
    if (filters?.category) params.append('category', filters.category);
    if (filters?.subcategory) params.append('subcategory', filters.subcategory);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.pricing) params.append('pricing', filters.pricing);
    if (filters?.verified !== undefined) params.append('verified', filters.verified.toString());
    if (filters?.sortBy) params.append('sortBy', filters.sortBy);
    if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const endpoint = `/tools${queryString ? `?${queryString}` : ''}`;
    
    return this.request(endpoint);
  }

  async getToolById(id: string): Promise<AITool> {
    return this.request(`/tools/${id}`);
  }

  async createTool(toolData: Partial<AITool>, apiKey: string): Promise<AITool> {
    return this.request('/tools', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(toolData),
    });
  }

  async updateTool(id: string, updates: Partial<AITool>, apiKey: string): Promise<AITool> {
    return this.request(`/tools/${id}`, {
      method: 'PUT',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(updates),
    });
  }

  async deleteTool(id: string, apiKey: string): Promise<void> {
    return this.request(`/tools/${id}`, {
      method: 'DELETE',
      headers: {
        'x-api-key': apiKey,
      },
    });
  }

  // Categories API
  async getCategories(): Promise<AICategory[]> {
    return this.request('/categories');
  }

  // Search API
  async searchTools(query: string, limit?: number): Promise<AITool[]> {
    const params = new URLSearchParams({ search: query });
    if (limit) params.append('limit', limit.toString());
    
    const result = await this.request<{
      data: AITool[];
      pagination: PaginationInfo;
    }>(`/tools?${params.toString()}`);
    
    return result.data;
  }

  // Tool submission API
  async submitTool(submission: {
    name: string;
    url: string;
    description: string;
    category: string;
    subcategory?: string;
    submitterName?: string;
    submitterEmail: string;
    logoUrl?: string;
    tags?: string[];
    pricingType?: string;
  }): Promise<void> {
    return this.request('/submissions', {
      method: 'POST',
      body: JSON.stringify(submission),
    });
  }

  // Content generation API (admin only)
  async generateContent(data: {
    url: string;
    scrapedData: any;
    pricingData?: any;
    faqData?: any;
  }, apiKey: string): Promise<any> {
    return this.request('/generate-content', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
      },
      body: JSON.stringify(data),
    });
  }

  // Web scraping API (admin only)
  async scrapeUrl(url: string, options?: any, apiKey?: string): Promise<any> {
    return this.request('/scrape', {
      method: 'POST',
      headers: apiKey ? { 'x-api-key': apiKey } : {},
      body: JSON.stringify({ url, options }),
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Convenience functions for common operations
export async function getToolsForCategory(categoryId: string, limit?: number): Promise<AITool[]> {
  const result = await apiClient.getTools({ 
    category: categoryId, 
    limit: limit || 20,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  return result.data;
}

export async function getFeaturedTools(limit: number = 10): Promise<AITool[]> {
  const result = await apiClient.getTools({ 
    verified: true,
    limit,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  return result.data;
}

export async function getToolsByPricing(pricingType: string, limit?: number): Promise<AITool[]> {
  const result = await apiClient.getTools({ 
    pricing: pricingType,
    limit: limit || 20,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  return result.data;
}

// Error handling utility
export function isApiError(error: unknown): error is Error {
  return error instanceof Error;
}

export function getErrorMessage(error: unknown): string {
  if (isApiError(error)) {
    return error.message;
  }
  return 'An unexpected error occurred';
}
