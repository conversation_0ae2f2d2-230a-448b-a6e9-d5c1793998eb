export interface Job {
  id: string;
  type: JobType;
  data: any;
  status: JobStatus;
  priority: JobPriority;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  updatedAt: Date;
  scheduledFor?: Date;
  completedAt?: Date;
  error?: string;
  result?: any;
}

export enum JobType {
  TOOL_SUBMISSION = 'tool_submission',
  CONTENT_GENERATION = 'content_generation',
  WEB_SCRAPING = 'web_scraping',
  EMAIL_NOTIFICATION = 'email_notification',
  TOOL_PROCESSING = 'tool_processing',
  SCREENSHOT_CAPTURE = 'screenshot_capture',
  FAVICON_EXTRACTION = 'favicon_extraction',
}

export enum JobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRYING = 'retrying',
  CANCELLED = 'cancelled',
}

export enum JobPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4,
}

export interface JobHandler {
  handle(job: Job): Promise<any>;
}

export interface JobQueue {
  add(type: JobType, data: any, options?: JobOptions): Promise<Job>;
  process(): Promise<void>;
  getJob(id: string): Promise<Job | null>;
  getJobs(status?: JobStatus): Promise<Job[]>;
  removeJob(id: string): Promise<boolean>;
  retryJob(id: string): Promise<Job>;
}

export interface JobOptions {
  priority?: JobPriority;
  delay?: number;
  maxAttempts?: number;
  scheduledFor?: Date;
}

export interface ToolSubmissionJobData {
  url: string;
  name: string;
  description?: string;
  category?: string;
  submitterEmail: string;
  submitterName?: string;
}

export interface ContentGenerationJobData {
  url: string;
  scrapedData: any;
  pricingData?: any;
  faqData?: any;
  toolId?: string;
}

export interface WebScrapingJobData {
  url: string;
  options?: {
    timeout?: number;
    waitForSelector?: string;
    extractImages?: boolean;
    extractLinks?: boolean;
  };
}

export interface EmailNotificationJobData {
  to: string | string[];
  subject: string;
  template: string;
  data: any;
  priority?: 'low' | 'normal' | 'high';
}
