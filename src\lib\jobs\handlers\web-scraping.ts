import { Job, <PERSON><PERSON><PERSON><PERSON>, WebScrapingJobData } from '../types';
import { contentProcessor } from '@/lib/scraping/content-processor';
import { EnhancedScrapeRequest, EnhancedScrapeResult, MediaAsset } from '@/lib/scraping/types';

interface JobResult {
  success: boolean;
  data: LegacyScrapedData;
  screenshot: string | null;
  scrapedAt: string;
  metadata?: {
    creditsUsed: number;
    optimizationStrategy: string;
    contentAnalysis: string;
    qualityScore: number;
  };
}

interface LegacyScrapedData {
  title: string;
  url: string;
  meta: Record<string, string>;
  text: string;
  headings: Array<{ level: string; text: string }>;
  links: Array<{ href: string; text: string }>;
  images: Array<{ src: string; alt: string }>;
  favicon: string | null;
  pricing: Array<{ text: string; tag: string }>;
  faq: Array<{ text: string; tag: string }>;
}

export class WebScrapingHandler implements JobHandler {
  async handle(job: Job): Promise<JobResult> {
    const data = job.data as WebScrapingJobData;

    try {
      console.log(`🕷️ Enhanced web scraping job started for: ${data.url}`);

      // Convert job data to enhanced scrape request
      const enhancedRequest: EnhancedScrapeRequest = {
        url: data.url,
        options: {
          timeout: data.options?.timeout || 30000,
          waitForSelector: data.options?.waitForSelector,
          outputFormat: 'markdown',
          enableJSRendering: false, // Start with basic scraping
          blockResources: true,
          deviceType: 'desktop'
        },
        costOptimization: true, // Enable cost optimization
        mediaCollection: data.options?.extractImages !== false, // Default true
        multiPageConfig: {
          enabled: false, // Disable multi-page for job queue to keep it simple
          mode: 'conditional',
          maxPagesPerTool: 2,
          creditThreshold: 50,
          pageTypes: {
            pricing: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
            faq: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
            features: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
            about: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false }
          },
          fallbackStrategy: { searchInMainPage: true, useNavigation: false, useSitemap: false }
        }
      };

      // Process enhanced scraping
      const result = await contentProcessor.processEnhancedScrape(enhancedRequest);

      if (!result.success) {
        throw new Error(result.error || 'Enhanced scraping failed');
      }

      // Convert enhanced result to legacy job format for backward compatibility
      const scrapedData = this.convertToLegacyFormat(result, data.options);

      console.log(`✅ Enhanced web scraping completed for: ${data.url} (${result.costAnalysis?.creditsUsed || 0} credits)`);

      return {
        success: true,
        data: scrapedData,
        screenshot: result.mediaAssets?.screenshot?.screenshot || null,
        scrapedAt: new Date().toISOString(),
        // Enhanced metadata
        metadata: {
          creditsUsed: result.costAnalysis?.creditsUsed || 0,
          optimizationStrategy: result.costAnalysis?.optimizationStrategy || 'unknown',
          contentAnalysis: result.contentAnalysis?.scenario || 'unknown',
          qualityScore: (result.metadata as Record<string, unknown>)?.qualityScore as number || 0
        }
      };
    } catch (error) {
      console.error('Enhanced web scraping failed:', error);
      throw new Error(`Enhanced web scraping failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Convert enhanced scrape result to legacy format for backward compatibility
   */
  private convertToLegacyFormat(result: EnhancedScrapeResult, options: WebScrapingJobData['options']): LegacyScrapedData {
    // Extract title and meta information from markdown content
    const title = this.extractTitleFromMarkdown(result.content);
    const headings = this.extractHeadingsFromMarkdown(result.content);
    const text = result.content.substring(0, 5000); // Limit text length

    return {
      title: title || 'Untitled',
      url: result.url || '',
      meta: {
        'og:title': title,
        'og:description': this.extractDescriptionFromMarkdown(result.content),
        description: this.extractDescriptionFromMarkdown(result.content)
      },
      text,
      headings,
      links: options?.extractLinks ? this.extractLinksFromMarkdown(result.content) : [],
      images: result.mediaAssets?.ogImages?.map((img: MediaAsset) => ({
        src: img.url,
        alt: ''
      })) || [],
      favicon: result.mediaAssets?.favicon?.[0] || null,
      pricing: this.extractSectionContent(result.content, ['price', 'pricing', 'cost', 'plan']),
      faq: this.extractSectionContent(result.content, ['faq', 'question', 'help', 'support'])
    };
  }

  private extractTitleFromMarkdown(content: string): string {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1].trim() : '';
  }

  private extractDescriptionFromMarkdown(content: string): string {
    const lines = content.split('\n');
    let foundHeading = false;

    for (const line of lines) {
      if (line.startsWith('#')) {
        foundHeading = true;
        continue;
      }
      if (foundHeading && line.trim() && !line.startsWith('#')) {
        return line.trim().substring(0, 200);
      }
    }

    return '';
  }

  private extractHeadingsFromMarkdown(content: string): Array<{ level: string; text: string }> {
    const headings: Array<{ level: string; text: string }> = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        const level = `h${match[1].length}`;
        const text = match[2].trim();
        headings.push({ level, text });
      }
    }

    return headings;
  }

  private extractLinksFromMarkdown(content: string): Array<{ href: string; text: string }> {
    const links: Array<{ href: string; text: string }> = [];
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    let match;

    while ((match = linkRegex.exec(content)) !== null) {
      links.push({
        text: match[1],
        href: match[2]
      });
    }

    return links;
  }

  private extractSectionContent(content: string, keywords: string[]): Array<{ text: string; tag: string }> {
    const sections: Array<{ text: string; tag: string }> = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (keywords.some(keyword => lowerLine.includes(keyword))) {
        sections.push({
          text: line.trim(),
          tag: line.startsWith('#') ? 'heading' : 'text'
        });
      }
    }

    return sections.slice(0, 10); // Limit to 10 items
  }
}
