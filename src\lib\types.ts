export interface AITool {
  id: string;
  name: string;
  logoUrl: string;
  description: string;
  link: string;
  tags?: {
    type: 'Trending' | 'New' | 'Premium';
    label?: string;
    icon?: string;
  }[];
  category: string;
  // Extended fields for tool detail pages
  detailedDescription?: string;
  features?: string[];
  screenshots?: string[];
  pricing?: {
    type: 'free' | 'freemium' | 'paid' | 'open source';
    plans?: {
      name: string;
      price: string;
      features: string[];
    }[];
  };
  reviews?: {
    rating: number;
    totalReviews: number;
    highlights?: string[];
  };
  website?: string;
  company?: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  // New fields for redesigned tool detail page
  subcategory?: string;
  isVerified?: boolean;
  isClaimed?: boolean;
  claimInfo?: {
    isClaimable: boolean;
    claimUrl?: string;
    claimInstructions?: string;
  };
  prosAndCons?: {
    pros: string[];
    cons: string[];
  };
  releases?: {
    version: string;
    date: string;
    notes: string;
    isLatest?: boolean;
  }[];

  // Enhanced AI System fields
  scrapedData?: ScrapedData;
  aiGenerationStatus?: AIGenerationStatus;
  lastScrapedAt?: string;
  editorialReviewId?: string;
  aiGenerationJobId?: string;
  submissionType?: SubmissionType;
  submissionSource?: string;
  contentQualityScore?: number;
  lastAiUpdate?: string;
  contentStatus?: ContentStatus;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
}

export interface AICategory {
  id: string;
  title: string;
  iconName: string;
  description: string;
  tools: AITool[];
  totalToolsCount: number;
  seeAllButton: {
    colorClass: string;
    textColorClass: string;
  };
}

export interface TooltipData {
  content: string;
  targetRect: DOMRect | null;
  position: 'top' | 'bottom' | 'left' | 'right';
}

export interface TopSearch {
  term: string;
  link: string;
}

export interface ToolFilters {
  search?: string;
  tags?: string[];
  pricing?: 'free' | 'freemium' | 'paid' | 'open source';
  verified?: boolean;
  sortBy?: 'name' | 'rating' | 'newest' | 'popular';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface CategoryPageData {
  category: AICategory;
  subcategory?: string;
  tools: AITool[];
  filters: ToolFilters;
  pagination: PaginationInfo;
}

export interface HomePageState {
  searchTerm: string;
  isSearchDropdownVisible: boolean;
  topSearches: TopSearch[];
  allCategories: AICategory[];
  searchResults: AITool[] | null;
  isLoadingCategories: boolean;
  isLoadingSearchResults: boolean;
  activeTooltip: TooltipData | null;
  isScrollToTopVisible: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  isExpanded?: boolean;
}

// =====================================================
// ENHANCED AI SYSTEM TYPES
// =====================================================

// AI Generation Status
export type AIGenerationStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';

// Content Status
export type ContentStatus = 'draft' | 'review' | 'published' | 'archived';

// Submission Types
export type SubmissionType = 'admin' | 'user_url' | 'user_full';

// Job Status
export type JobStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'paused';

// Job Types
export type JobType = 'scrape' | 'generate' | 'bulk' | 'media_extraction';

// Asset Types
export type AssetType = 'logo' | 'favicon' | 'screenshot' | 'og_image';

// Review Status
export type ReviewStatus = 'pending' | 'approved' | 'rejected' | 'needs_revision';

// Scraped Data Interface
export interface ScrapedData {
  url: string;
  title?: string;
  description?: string;
  content: string; // Markdown content
  metadata: {
    ogImage?: string;
    favicon?: string;
    keywords?: string[];
    author?: string;
    publishedDate?: string;
  };
  pricing?: {
    detected: boolean;
    plans?: any[];
    freeOption?: boolean;
  };
  features?: string[];
  screenshots?: string[];
  scrapedAt: string;
  scrapeMethod: string; // 'scrape.do', 'manual', etc.
}

// AI Generation Job Interface
export interface AIGenerationJob {
  id: string;
  toolId: string;
  jobType: JobType;
  status: JobStatus;
  progress: number; // 0-100
  scrapedData?: ScrapedData;
  aiPrompts?: {
    systemPrompt: string;
    userPrompts: string[];
    modelUsed: string;
  };
  aiResponses?: {
    generatedContent: any;
    tokensUsed: number;
    cost: number;
    processingTime: number;
  };
  errorLogs?: {
    timestamp: string;
    error: string;
    stackTrace?: string;
  }[];
  processingOptions?: {
    aiProvider: 'openai' | 'openrouter';
    model: string;
    temperature: number;
    maxTokens: number;
  };
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
}

// Media Asset Interface
export interface MediaAsset {
  id: string;
  toolId: string;
  assetType: AssetType;
  sourceUrl?: string;
  localPath?: string;
  cdnUrl?: string;
  fileSize?: number;
  mimeType?: string;
  width?: number;
  height?: number;
  altText?: string;
  isPrimary: boolean;
  extractionMethod: string;
  createdAt: string;
  updatedAt: string;
}

// Editorial Review Interface
export interface EditorialReview {
  id: string;
  toolId: string;
  reviewedBy: string;
  reviewStatus: ReviewStatus;
  reviewDate: string;
  featuredDate?: string;
  reviewNotes?: string;
  editorialText?: string; // "was manually vetted by our editorial team and was first featured on [date]"
  qualityScore?: number; // 1-10
  contentFlags?: string[];
  approvalWorkflow?: {
    currentStep: string;
    history: {
      step: string;
      timestamp: string;
      user: string;
      notes?: string;
    }[];
  };
  createdAt: string;
  updatedAt: string;
}

// Bulk Processing Job Interface
export interface BulkProcessingJob {
  id: string;
  jobType: 'text_file' | 'json_file' | 'manual_entry' | 'csv_import';
  status: JobStatus;
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  sourceData: {
    filename?: string;
    urls?: string[];
    tools?: Partial<AITool>[];
  };
  processingOptions: {
    batchSize: number;
    delayBetweenBatches: number;
    retryAttempts: number;
    aiProvider: 'openai' | 'openrouter';
    skipExisting: boolean;
  };
  results: {
    successful: {
      toolId: string;
      url: string;
      generatedAt: string;
    }[];
    failed: {
      url: string;
      error: string;
      timestamp: string;
    }[];
  };
  progressLog: {
    timestamp: string;
    message: string;
    level: 'info' | 'warning' | 'error';
  }[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
}

// System Configuration Interface
export interface SystemConfiguration {
  id: string;
  configKey: string;
  configValue: any;
  configType: 'ai_provider' | 'scraping' | 'job_processing' | 'system' | 'security';
  isSensitive: boolean;
  isActive: boolean;
  description?: string;
  validationSchema?: any;
  updatedBy?: string;
  version: number;
  createdAt: string;
  updatedAt: string;
}

// =====================================================
// ADMIN PANEL INTERFACES
// =====================================================

// Admin Dashboard Stats
export interface AdminDashboardStats {
  totalTools: number;
  publishedTools: number;
  draftTools: number;
  pendingReview: number;
  activeJobs: number;
  completedJobs: number;
  failedJobs: number;
  totalMediaAssets: number;
  systemHealth: {
    status: 'healthy' | 'warning' | 'error';
    uptime: number;
    lastCheck: string;
  };
}

// Job Monitoring Interface
export interface JobMonitoringData {
  activeJobs: AIGenerationJob[];
  recentJobs: AIGenerationJob[];
  jobStats: {
    totalJobs: number;
    successRate: number;
    averageProcessingTime: number;
    costToday: number;
    costThisMonth: number;
  };
  systemMetrics: {
    queueLength: number;
    processingCapacity: number;
    errorRate: number;
  };
}

// Bulk Processing Interface
export interface BulkProcessingData {
  activeJobs: BulkProcessingJob[];
  recentJobs: BulkProcessingJob[];
  templates: {
    textFile: string;
    jsonFile: any;
    csvFile: string;
  };
  processingOptions: {
    defaultBatchSize: number;
    maxConcurrentJobs: number;
    supportedFormats: string[];
  };
}

// Editorial Workflow Interface
export interface EditorialWorkflowData {
  pendingReviews: (AITool & { editorialReview?: EditorialReview })[];
  recentReviews: EditorialReview[];
  reviewStats: {
    totalReviews: number;
    approvalRate: number;
    averageReviewTime: number;
    featuredTools: number;
  };
  workflowSettings: {
    autoApprovalThreshold: number;
    requiredReviewers: number;
    escalationRules: any[];
  };
}

// User Submission Management
export interface UserSubmissionData {
  pendingSubmissions: {
    id: string;
    url: string;
    name: string;
    description: string;
    submitterEmail: string;
    submittedAt: string;
    status: 'pending' | 'approved' | 'rejected';
  }[];
  submissionStats: {
    totalSubmissions: number;
    approvalRate: number;
    averageProcessingTime: number;
    topCategories: { category: string; count: number }[];
  };
}

// Configuration Management Interface
export interface ConfigurationManagementData {
  aiProviders: {
    openai: {
      enabled: boolean;
      apiKey: string;
      model: string;
      maxTokens: number;
      temperature: number;
    };
    openrouter: {
      enabled: boolean;
      apiKey: string;
      model: string;
      maxTokens: number;
      temperature: number;
    };
  };
  scraping: {
    scrapeDoApiKey: string;
    defaultTimeout: number;
    costOptimizationEnabled: boolean;
    maxRetries: number;
  };
  jobProcessing: {
    maxConcurrentJobs: number;
    defaultRetryAttempts: number;
    batchSize: number;
    processingDelay: number;
  };
  system: {
    contentQualityThreshold: number;
    autoApprovalEnabled: boolean;
    debugMode: boolean;
    maintenanceMode: boolean;
  };
}

// API Response Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

// Pagination Interface
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Search and Filter Interfaces
export interface AdminToolFilters extends ToolFilters {
  aiGenerationStatus?: AIGenerationStatus;
  contentStatus?: ContentStatus;
  submissionType?: SubmissionType;
  hasEditorialReview?: boolean;
  qualityScoreMin?: number;
  qualityScoreMax?: number;
  lastScrapedAfter?: string;
  lastScrapedBefore?: string;
}

// WebSocket Event Types for Real-time Updates
export interface WebSocketEvent {
  type: 'job_update' | 'job_completed' | 'job_failed' | 'system_alert' | 'bulk_progress';
  data: any;
  timestamp: string;
}
