// Main exports for the job system
export { getJobQueue } from './queue';
export { initializeJobQueue } from './init';
export * from './types';
export * from './handlers';

// Convenience functions
import { getJobQueue } from './queue';
import { JobType, JobPriority } from './types';

/**
 * Queue a tool submission for processing
 */
export async function queueToolSubmission(data: {
  url: string;
  name: string;
  description?: string;
  category?: string;
  submitterEmail: string;
  submitterName?: string;
}) {
  const queue = getJobQueue();
  return queue.add(JobType.TOOL_SUBMISSION, data, {
    priority: JobPriority.NORMAL,
  });
}

/**
 * Queue content generation for a tool
 */
export async function queueContentGeneration(data: {
  url: string;
  scrapedData: any;
  toolId?: string;
}) {
  const queue = getJobQueue();
  return queue.add(JobType.CONTENT_GENERATION, data, {
    priority: JobPriority.NORMAL,
  });
}

/**
 * Queue web scraping for a URL
 */
export async function queueWebScraping(data: {
  url: string;
  options?: any;
}) {
  const queue = getJobQueue();
  return queue.add(JobType.WEB_SCRAPING, data, {
    priority: JobPriority.NORMAL,
  });
}

/**
 * Queue an email notification
 */
export async function queueEmailNotification(data: {
  to: string | string[];
  subject: string;
  template: string;
  data: any;
}) {
  const queue = getJobQueue();
  return queue.add(JobType.EMAIL_NOTIFICATION, data, {
    priority: JobPriority.LOW,
  });
}
