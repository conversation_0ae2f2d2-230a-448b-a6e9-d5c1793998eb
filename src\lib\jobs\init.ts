import { getJobQueue } from './queue';

let initialized = false;

export function initializeJobQueue() {
  if (initialized) {
    return;
  }

  if (process.env.JOB_QUEUE_ENABLED !== 'true') {
    console.log('Job queue is disabled');
    return;
  }

  try {
    const queue = getJobQueue();
    console.log('✅ Job queue initialized successfully');
    
    // Log queue configuration
    console.log(`📊 Job queue configuration:
- Max concurrent jobs: ${process.env.MAX_CONCURRENT_JOBS || '3'}
- Retry attempts: ${process.env.JOB_RETRY_ATTEMPTS || '3'}
- Content generation: ${process.env.CONTENT_GENERATION_ENABLED === 'true' ? 'enabled' : 'disabled'}
- Email notifications: ${process.env.SMTP_USER ? 'enabled' : 'disabled'}`);

    initialized = true;
  } catch (error) {
    console.error('❌ Failed to initialize job queue:', error);
  }
}

// Auto-initialize in production
if (process.env.NODE_ENV === 'production') {
  initializeJobQueue();
}
