import { NextRequest, NextResponse } from 'next/server';
import { getTools, createTool } from '@/lib/supabase';
import { validateApi<PERSON>ey } from '@/lib/auth';
import { getJobQueue } from '@/lib/jobs/queue';
import { JobType, JobPriority } from '@/lib/jobs/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const subcategory = searchParams.get('subcategory');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const pricing = searchParams.get('pricing');
    const verified = searchParams.get('verified') === 'true' ? true : undefined;
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

    const result = await getTools({
      category: category || undefined,
      subcategory: subcategory || undefined,
      page,
      limit,
      search: search || undefined,
      pricing: pricing || undefined,
      verified,
      sortBy,
      sortOrder,
    });

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching tools:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tools' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const toolData = await request.json();
    
    // Generate slug from name
    const slug = toolData.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '');

    const newTool = await createTool({
      ...toolData,
      slug,
      content_status: 'draft',
      created_at: new Date().toISOString(),
    });

    // Trigger post-creation tasks (if enabled)
    if (process.env.JOB_QUEUE_ENABLED === 'true') {
      try {
        const queue = getJobQueue();

        // Queue content generation if tool doesn't have generated content
        if (!newTool.generated_content && newTool.url) {
          await queue.add(
            JobType.CONTENT_GENERATION,
            {
              url: newTool.url,
              toolId: newTool.id,
              scrapedData: null, // Will be scraped first
            },
            {
              priority: JobPriority.NORMAL,
            }
          );
        }

        // Queue email notification for admin
        if (process.env.ADMIN_EMAIL) {
          await queue.add(
            JobType.EMAIL_NOTIFICATION,
            {
              to: process.env.ADMIN_EMAIL,
              subject: 'New Tool Created - AI Dude Directory',
              template: 'admin-tool-created',
              data: {
                toolName: newTool.name,
                toolUrl: newTool.url,
                toolId: newTool.id,
              },
            },
            {
              priority: JobPriority.LOW,
            }
          );
        }

        console.log('Post-creation jobs queued successfully');
      } catch (jobError) {
        console.error('Failed to queue post-creation jobs:', jobError);
        // Don't fail the request if job queueing fails
      }
    }

    return NextResponse.json({
      success: true,
      data: newTool,
    });
  } catch (error) {
    console.error('Error creating tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create tool' },
      { status: 500 }
    );
  }
}
