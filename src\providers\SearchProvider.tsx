'use client';

import React, { createContext, useContext } from 'react';
import { useSearch } from '@/hooks/useSearch';

interface SearchContextType {
  searchTerm: string;
  isSearchDropdownVisible: boolean;
  searchResults: any[];
  isLoadingSearchResults: boolean;
  handleSearchFocus: () => void;
  handleSearchBlur: () => void;
  handleSearchChange: (value: string) => void;
  handleTopSearchClick: (term: string) => void;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export function SearchProvider({ children }: { children: React.ReactNode }) {
  const searchState = useSearch();

  return (
    <SearchContext.Provider value={searchState}>
      {children}
    </SearchContext.Provider>
  );
}

export function useSearchContext() {
  const context = useContext(SearchContext);
  if (context === undefined) {
    throw new Error('useSearchContext must be used within a SearchProvider');
  }
  return context;
}
