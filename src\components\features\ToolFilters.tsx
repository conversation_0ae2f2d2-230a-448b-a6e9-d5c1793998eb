'use client';

import React from 'react';
import { Search, Filter, SortAsc, SortDesc, X } from 'lucide-react';
import { ToolFilters } from '@/lib/types';

interface ToolFiltersProps {
  filters: ToolFilters;
  onFiltersChange: (filters: ToolFilters) => void;
  totalResults: number;
  className?: string;
}

const TAG_OPTIONS = [
  { value: 'Trending', label: 'Trending' },
  { value: 'New', label: 'New' },
  { value: 'Premium', label: 'Premium' },
];

const PRICING_OPTIONS = [
  { value: 'free', label: 'Free' },
  { value: 'freemium', label: 'Freemium' },
  { value: 'paid', label: 'Paid' },
  { value: 'open source', label: 'Open Source' },
];

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'rating', label: 'Rating' },
  { value: 'newest', label: 'Newest' },
  { value: 'popular', label: 'Popular' },
];

export function ToolFilters({ filters, onFiltersChange, totalResults, className = '' }: ToolFiltersProps) {
  const updateFilters = (updates: Partial<ToolFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      tags: [],
      pricing: undefined,
      verified: undefined,
      sortBy: 'name',
      sortOrder: 'asc',
    });
  };

  const hasActiveFilters = Boolean(
    filters.search ||
    (filters.tags && filters.tags.length > 0) ||
    filters.pricing ||
    filters.verified !== undefined
  );

  const toggleTag = (tag: string) => {
    const currentTags = filters.tags || [];
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    updateFilters({ tags: newTags });
  };

  const toggleSortOrder = () => {
    updateFilters({ 
      sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' 
    });
  };

  return (
    <div className={`bg-zinc-800 border border-black rounded-lg p-4 shadow-lg ${className}`}>
      
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter size={18} className="text-gray-400" />
          <h3 className="text-white font-medium">Filters</h3>
          <span className="text-gray-400 text-sm">({totalResults} tools)</span>
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-gray-400 hover:text-white text-sm flex items-center gap-1 transition-colors duration-200"
          >
            <X size={14} />
            Clear
          </button>
        )}
      </div>

      {/* Search */}
      <div className="mb-3">
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={filters.search || ''}
            onChange={(e) => updateFilters({ search: e.target.value })}
            placeholder="Search tools..."
            className="w-full bg-zinc-700 text-white placeholder-gray-400 pl-10 pr-4 py-2 rounded-lg border border-zinc-600 focus:border-orange-500 focus:ring-1 focus:ring-orange-500 outline-none transition-all duration-200 text-sm"
          />
        </div>
      </div>

      {/* Tags - positioned directly below search */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-2">
          {TAG_OPTIONS.map((tag) => {
            const isSelected = filters.tags?.includes(tag.value) || false;
            return (
              <button
                key={tag.value}
                onClick={() => toggleTag(tag.value)}
                className={`px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200 border ${
                  isSelected
                    ? 'bg-orange-500 border-orange-500 text-white'
                    : 'bg-zinc-700 border-zinc-600 text-gray-300 hover:bg-zinc-600'
                }`}
              >
                {tag.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Pricing */}
      <div className="mb-4">
        <label className="text-gray-300 text-sm font-medium mb-2 block">Pricing</label>
        <select
          value={filters.pricing || ''}
          onChange={(e) => updateFilters({ pricing: e.target.value as any || undefined })}
          className="w-full bg-zinc-700 text-white border border-zinc-600 rounded-lg px-3 py-2 text-sm focus:border-orange-500 focus:ring-1 focus:ring-orange-500 outline-none transition-all duration-200"
        >
          <option value="">All pricing</option>
          {PRICING_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Verified */}
      <div className="mb-4">
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={filters.verified || false}
            onChange={(e) => updateFilters({ verified: e.target.checked || undefined })}
            className="w-4 h-4 text-orange-500 bg-zinc-700 border-zinc-600 rounded focus:ring-orange-500 focus:ring-2"
          />
          <span className="text-gray-300 text-sm">Verified tools only</span>
        </label>
      </div>

      {/* Sort */}
      <div>
        <label className="text-gray-300 text-sm font-medium mb-2 block">Sort by</label>
        <div className="flex gap-2">
          <select
            value={filters.sortBy || 'name'}
            onChange={(e) => updateFilters({ sortBy: e.target.value as any })}
            className="flex-1 bg-zinc-700 text-white border border-zinc-600 rounded-lg px-3 py-2 text-sm focus:border-orange-500 focus:ring-1 focus:ring-orange-500 outline-none transition-all duration-200"
          >
            {SORT_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          <button
            onClick={toggleSortOrder}
            className="bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 hover:bg-zinc-600 transition-colors duration-200"
            title={`Sort ${filters.sortOrder === 'asc' ? 'descending' : 'ascending'}`}
          >
            {filters.sortOrder === 'asc' ? (
              <SortAsc size={16} className="text-gray-300" />
            ) : (
              <SortDesc size={16} className="text-gray-300" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
