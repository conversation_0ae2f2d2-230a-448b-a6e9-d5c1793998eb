import { NextRequest, NextResponse } from 'next/server';
import { getJobQueue } from '@/lib/jobs/queue';
import { validateApiKey } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const queue = getJobQueue();
    const job = await queue.getJob(params.id);

    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: job.id,
        type: job.type,
        status: job.status,
        priority: job.priority,
        attempts: job.attempts,
        maxAttempts: job.maxAttempts,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        scheduledFor: job.scheduledFor,
        completedAt: job.completedAt,
        error: job.error,
        result: job.result,
        data: job.data,
      },
    });
  } catch (error) {
    console.error('Error fetching job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch job' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action } = await request.json();

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Action is required' },
        { status: 400 }
      );
    }

    const queue = getJobQueue();

    switch (action) {
      case 'retry':
        const retriedJob = await queue.retryJob(params.id);
        return NextResponse.json({
          success: true,
          data: {
            id: retriedJob.id,
            status: retriedJob.status,
            attempts: retriedJob.attempts,
            updatedAt: retriedJob.updatedAt,
          },
        });

      case 'cancel':
        const job = await queue.getJob(params.id);
        if (!job) {
          return NextResponse.json(
            { success: false, error: 'Job not found' },
            { status: 404 }
          );
        }

        if (job.status === 'processing') {
          return NextResponse.json(
            { success: false, error: 'Cannot cancel job that is currently processing' },
            { status: 400 }
          );
        }

        job.status = 'cancelled' as any;
        job.updatedAt = new Date();

        return NextResponse.json({
          success: true,
          data: {
            id: job.id,
            status: job.status,
            updatedAt: job.updatedAt,
          },
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error performing job action:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform job action' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const queue = getJobQueue();
    const removed = await queue.removeJob(params.id);

    if (!removed) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Job removed successfully',
    });
  } catch (error) {
    console.error('Error removing job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to remove job' },
      { status: 500 }
    );
  }
}
