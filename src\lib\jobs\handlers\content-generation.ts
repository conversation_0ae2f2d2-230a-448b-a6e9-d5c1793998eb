import { Job, JobHandler, ContentGenerationJobData } from '../types';
import OpenAI from 'openai';

export class ContentGenerationHandler implements JobHandler {
  private _openai: OpenAI | null = null;

  private get openai() {
    if (!this._openai) {
      this._openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    }
    return this._openai;
  }

  async handle(job: Job): Promise<any> {
    const data = job.data as ContentGenerationJobData;
    
    if (!process.env.CONTENT_GENERATION_ENABLED || process.env.CONTENT_GENERATION_ENABLED !== 'true') {
      throw new Error('Content generation is disabled');
    }

    try {
      const prompt = this.buildPrompt(data);
      const response = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: `You are "AI Dude," the no-BS curator of AI tools. Write in an irreverent, witty tone that's both informative and blunt. You must follow the exact JSON structure provided and ensure all content is engaging, SEO-friendly, and maintains the distinctive brand voice.`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.8,
        max_tokens: 4000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content generated from OpenAI');
      }

      // Parse the JSON response (handle markdown code blocks)
      let parsedContent;
      try {
        // Remove markdown code blocks if present
        let cleanContent = content.trim();
        if (cleanContent.startsWith('```json')) {
          cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        parsedContent = JSON.parse(cleanContent);
      } catch (parseError) {
        throw new Error(`Failed to parse AI response as JSON: ${parseError}. Content: ${content.substring(0, 200)}...`);
      }

      // Validate required fields
      this.validateGeneratedContent(parsedContent);

      return {
        success: true,
        content: parsedContent,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Content generation failed:', error);
      throw error;
    }
  }

  private buildPrompt(data: ContentGenerationJobData): string {
    const { url, scrapedData, pricingData, faqData } = data;

    return `
Generate comprehensive AI tool content for: ${url}

Scraped Data:
${JSON.stringify(scrapedData, null, 2)}

${pricingData ? `Pricing Data:\n${JSON.stringify(pricingData, null, 2)}\n` : ''}
${faqData ? `FAQ Data:\n${JSON.stringify(faqData, null, 2)}\n` : ''}

Generate content in this EXACT JSON structure:

{
  "toolName": "string (extracted from scraped data)",
  "toolDescription": "string (max 500 chars, punchy one-liner)",
  "detailedDescription": "string (150-300 words, comprehensive overview)",
  "keyFeatures": ["string", "string", "string"] (3-8 features),
  "prosAndCons": {
    "pros": ["string", "string", "string"] (3-10 pros),
    "cons": ["string", "string", "string"] (3-10 cons)
  },
  "pricingType": "Free|Paid|Freemium|Open Source",
  "pricingDetails": "string (brief pricing summary)",
  "categories": {
    "primary": "string (main category)",
    "secondary": "string (subcategory)",
    "confidence": 0.95 (0-1 confidence score)
  },
  "sampleQA": [
    {
      "question": "string",
      "answer": "string"
    }
  ] (3-6 Q&As),
  "tags": ["string", "string"] (5-10 relevant hashtags/keywords),
  "tooltip": "string (short 1-line description)",
  "haiku": "string (3-line haiku about the tool)",
  "seoKeywords": ["string", "string"] (5-8 SEO keywords),
  "releases": null (only include if found in scraped data)
}

Requirements:
- Use irreverent, witty tone like ThePornDude but for AI tools
- Vary sentence structures and use idiomatic expressions
- Make content feel human-written, not AI-generated
- Be informative but entertaining
- Include humor where appropriate
- Ensure all text is SEO-friendly
- Extract actual features and pricing from scraped data
- Categorize accurately based on tool functionality
- Generate realistic Q&As that users would actually ask
- Create relevant hashtags and keywords
- Keep tooltip concise but descriptive
- Make haiku creative and tool-specific
`;
  }

  private validateGeneratedContent(content: any): void {
    const requiredFields = [
      'toolName',
      'toolDescription',
      'detailedDescription',
      'keyFeatures',
      'prosAndCons',
      'pricingType',
      'categories',
      'sampleQA',
      'tags',
      'tooltip',
      'haiku',
      'seoKeywords',
    ];

    for (const field of requiredFields) {
      if (!(field in content)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate specific field types and constraints
    if (content.toolDescription.length > 500) {
      throw new Error('Tool description exceeds 500 character limit');
    }

    const wordCount = content.detailedDescription.split(' ').length;
    if (wordCount < 50 || wordCount > 500) {
      console.warn(`Detailed description word count (${wordCount}) is outside recommended range (150-300 words)`);
    }

    if (!Array.isArray(content.keyFeatures) || content.keyFeatures.length < 3 || content.keyFeatures.length > 8) {
      throw new Error('Key features must be an array of 3-8 items');
    }

    if (!['Free', 'Paid', 'Freemium', 'Open Source'].includes(content.pricingType)) {
      throw new Error('Invalid pricing type');
    }

    if (!content.categories.primary || !content.categories.secondary) {
      throw new Error('Categories must include primary and secondary');
    }

    if (!Array.isArray(content.sampleQA) || content.sampleQA.length < 3 || content.sampleQA.length > 6) {
      throw new Error('Sample Q&A must be an array of 3-6 items');
    }

    if (!Array.isArray(content.tags) || content.tags.length < 5 || content.tags.length > 10) {
      throw new Error('Tags must be an array of 5-10 items');
    }
  }
}
