'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { AICategory, AITool, ToolFilters, PaginationInfo } from '@/lib/types';
import { CategoryBreadcrumb } from './CategoryBreadcrumb';
import { ToolFilters as ToolFiltersComponent } from './ToolFilters';
import { ToolGrid } from './ToolGrid';
import { ToolPagination } from './ToolPagination';
import { FeaturedTools } from './FeaturedTools';
import { filterTools, sortTools, paginateTools, generateBreadcrumbs } from '@/lib/categoryUtils';

interface CategoryToolsPageProps {
  category: AICategory;
  subcategory?: string;
  initialTools: AITool[];
}

export function CategoryToolsPage({ category, subcategory, initialTools }: CategoryToolsPageProps) {
  const [filters, setFilters] = useState<ToolFilters>({
    search: '',
    tags: [],
    pricing: undefined,
    verified: undefined,
    sortBy: 'name',
    sortOrder: 'asc',
  });
  
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 24;

  // Generate breadcrumbs (simplified for sync operation)
  const breadcrumbs = useMemo(() => {
    const breadcrumbs = [
      { label: 'Home', href: '/' },
      { label: category.title, href: `/category/${category.id}` },
    ];

    if (subcategory) {
      breadcrumbs.push({
        label: subcategory,
        href: `/category/${category.id}/${subcategory.toLowerCase().replace(/\s+/g, '-')}`,
      });
    }

    return breadcrumbs;
  }, [category.id, category.title, subcategory]);

  // Filter and sort tools
  const processedTools = useMemo(() => {
    let filtered = filterTools(initialTools, filters);
    return sortTools(filtered, filters.sortBy || 'name', filters.sortOrder);
  }, [initialTools, filters]);

  // Paginate tools
  const { tools: paginatedTools, pagination } = useMemo(() => 
    paginateTools(processedTools, currentPage, itemsPerPage),
    [processedTools, currentPage, itemsPerPage]
  );

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Page title
  const pageTitle = subcategory 
    ? `${subcategory} - ${category.title}`
    : category.title;

  // Page description
  const pageDescription = subcategory
    ? `Discover the best ${subcategory.toLowerCase()} tools in ${category.title.toLowerCase()}`
    : category.description;

  const handleFiltersChange = (newFilters: ToolFilters) => {
    setFilters(newFilters);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of results
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="w-full">
      <div className="mx-auto px-4 py-6" style={{ maxWidth: 'var(--container-width)' }}>
        
        {/* Breadcrumb */}
        <CategoryBreadcrumb items={breadcrumbs} className="mb-6" />
        
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-3">
            {pageTitle}
          </h1>
          <p className="text-gray-400 text-lg max-w-3xl">
            {pageDescription}
          </p>
        </div>

        {/* Main Content Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          
          {/* LEFT SIDE - Filters and Main Content (3/4 width) */}
          <div className="lg:col-span-3 space-y-6">
            
            {/* Filters */}
            <ToolFiltersComponent
              filters={filters}
              onFiltersChange={handleFiltersChange}
              totalResults={processedTools.length}
            />
            
            {/* Tools Grid */}
            <ToolGrid
              tools={paginatedTools}
              showCategory={!subcategory} // Show category when viewing all tools
              showSubcategory={false} // Don't show subcategory in category pages
              emptyMessage={
                subcategory 
                  ? `No ${subcategory.toLowerCase()} tools found matching your criteria.`
                  : `No tools found in ${category.title.toLowerCase()} matching your criteria.`
              }
            />
            
            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <ToolPagination
                pagination={pagination}
                onPageChange={handlePageChange}
                className="mt-8"
              />
            )}
          </div>

          {/* RIGHT SIDE - Featured Tools Sidebar (1/4 width) */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <FeaturedTools />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
